import { BoundingBox, OCRResult } from '@/types';
import Tesseract from 'tesseract.js';

export class OCRService {
  private static instance: OCRService;
  private worker: Tesseract.Worker | null = null;

  private constructor() {}

  public static getInstance(): OCRService {
    if (!OCRService.instance) {
      OCRService.instance = new OCRService();
    }
    return OCRService.instance;
  }

  private async initializeWorker(logger?: (m: any) => void): Promise<Tesseract.Worker> {
    if (!this.worker) {
      const options = logger ? { logger } : {};
      this.worker = await Tesseract.createWorker('eng', 1, options);
      await this.worker.setParameters({
        tessedit_pageseg_mode: Tesseract.PSM.SPARSE_TEXT,
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?;:()[]{}"\'-+=*/\\|@#$%^&_~`<> \n\t',
      });
    }
    return this.worker;
  }

  public async extractText(
    imageFile: File,
    onProgress?: (progress: number) => void
  ): Promise<OCRResult> {
    try {
      const logger = onProgress ? (m: any) => {
        if (m.status === 'recognizing text') {
          onProgress(m.progress);
        }
      } : undefined;

      const worker = await this.initializeWorker(logger);
      const { data } = await worker.recognize(imageFile);

      // Extract words from the hierarchical structure: blocks -> paragraphs -> lines -> words
      const boundingBoxes: BoundingBox[] = [];
      if (data.blocks) {
        for (const block of data.blocks) {
          for (const paragraph of block.paragraphs) {
            for (const line of paragraph.lines) {
              for (const word of line.words) {
                if (word.confidence > 30) { // Filter out low-confidence words
                  boundingBoxes.push({
                    x: word.bbox.x0,
                    y: word.bbox.y0,
                    width: word.bbox.x1 - word.bbox.x0,
                    height: word.bbox.y1 - word.bbox.y0,
                    text: word.text,
                  });
                }
              }
            }
          }
        }
      }

      return {
        text: data.text.trim(),
        confidence: data.confidence,
        boundingBoxes,
      };
    } catch (error) {
      console.error('OCR extraction failed:', error);
      throw new Error('Failed to extract text from image. Please try again.');
    }
  }

  public async extractTextFromMultipleImages(
    imageFiles: File[],
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<OCRResult[]> {
    const results: OCRResult[] = [];
    
    for (let i = 0; i < imageFiles.length; i++) {
      const file = imageFiles[i];
      const result = await this.extractText(file, (progress) => {
        onProgress?.(i, progress);
      });
      results.push(result);
    }
    
    return results;
  }

  public async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
    }
  }

  // Utility method to preprocess image for better OCR results
  public static preprocessImage(canvas: HTMLCanvasElement): HTMLCanvasElement {
    const ctx = canvas.getContext('2d');
    if (!ctx) return canvas;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Convert to grayscale and increase contrast
    for (let i = 0; i < data.length; i += 4) {
      const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
      
      // Increase contrast
      const contrast = 1.5;
      const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
      const enhancedGray = factor * (gray - 128) + 128;
      
      data[i] = enhancedGray;     // Red
      data[i + 1] = enhancedGray; // Green
      data[i + 2] = enhancedGray; // Blue
      // Alpha channel remains unchanged
    }

    ctx.putImageData(imageData, 0, 0);
    return canvas;
  }

  // Method to convert file to canvas for preprocessing
  public static async fileToCanvas(file: File): Promise<HTMLCanvasElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        resolve(canvas);
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = URL.createObjectURL(file);
    });
  }
}

// Export singleton instance
export const ocrService = OCRService.getInstance();
